{"header": {"event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20250807.143608.50922.0.001.json", "dumpEventTime": "2025-08-07T14:36:08Z", "dumpEventTimeStamp": "1754548568974", "processId": 50922, "cwd": "/home/<USER>/tzlib/renbao/pdfc-web-*******", "commandLine": ["node", "/home/<USER>/tzlib/renbao/pdfc-web-*******/node_modules/.bin/vue-cli-service", "serve"], "nodejsVersion": "v12.1.0", "glibcVersionRuntime": "2.42", "glibcVersionCompiler": "2.17", "wordSize": 64, "arch": "x64", "platform": "linux", "componentVersions": {"node": "12.1.0", "v8": "7.4.288.21-node.16", "uv": "1.28.0", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.38.0", "napi": "4", "llhttp": "1.1.1", "http_parser": "2.8.0", "openssl": "1.1.1b", "cldr": "35.1", "icu": "64.2", "tz": "2019a", "unicode": "12.1"}, "release": {"name": "node", "headersUrl": "https://nodejs.org/download/release/v12.1.0/node-v12.1.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.1.0/node-v12.1.0.tar.gz"}, "osName": "Linux", "osRelease": "6.15.8-arch1-2", "osVersion": "#1 SMP PREEMPT_DYNAMIC Tue, 29 Jul 2025 15:05:00 +0000", "osMachine": "x86_64", "host": "xyzmsi"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x0000000000aa766d", "symbol": "report::TriggerNodeReport(v8::Isolate*, node::Environment*, char const*, char const*, std::string const&, v8::Local<v8::String>) [node]"}, {"pc": "0x000000000098c903", "symbol": "node::OnFatalError(char const*, char const*) [node]"}, {"pc": "0x0000000000b050de", "symbol": "v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [node]"}, {"pc": "0x0000000000b05459", "symbol": "v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [node]"}, {"pc": "0x0000000000f10515", "symbol": " [node]"}, {"pc": "0x0000000000f1ae7b", "symbol": "v8::internal::Heap::PerformGarbageCollection(v8::internal::GarbageCollector, v8::GCCallbackFlags) [node]"}, {"pc": "0x0000000000f1bb97", "symbol": "v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [node]"}, {"pc": "0x0000000000f1e635", "symbol": "v8::internal::Heap::AllocateRawWithRetryOrFail(int, v8::internal::AllocationType, v8::internal::AllocationAlignment) [node]"}, {"pc": "0x0000000000ee9660", "symbol": "v8::internal::Factory::NewFillerObject(int, bool, v8::internal::AllocationSpace) [node]"}, {"pc": "0x00000000011bb46e", "symbol": "v8::internal::Runtime_AllocateInNewSpace(int, unsigned long*, v8::internal::Isolate*) [node]"}, {"pc": "0x0000000001a80fe2", "symbol": " [node]"}], "javascriptHeap": {"totalMemory": 2168107008, "totalCommittedMemory": 2164228624, "usedMemory": 2072656448, "availableMemory": 62750768, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 524288, "committedMemory": 32024, "capacity": 523976, "used": 31712, "available": 492264}, "new_space": {"memorySize": 33554432, "committedMemory": 31458072, "capacity": 16767232, "used": 1263408, "available": 15503824}, "old_space": {"memorySize": 2025971712, "committedMemory": 2025590424, "capacity": 1971757984, "used": 1971478824, "available": 279160}, "code_space": {"memorySize": 4882432, "committedMemory": 4459808, "capacity": 4146080, "used": 4146080, "available": 0}, "map_space": {"memorySize": 12587008, "committedMemory": 12101160, "capacity": 5380880, "used": 5380880, "available": 0}, "large_object_space": {"memorySize": 89088000, "committedMemory": 89088000, "capacity": 88976088, "used": 88976088, "available": 0}, "code_large_object_space": {"memorySize": 1499136, "committedMemory": 1499136, "capacity": 1379456, "used": 1379456, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 16767232, "used": 0, "available": 16767232}}}, "resourceUsage": {"userCpuSeconds": 133.504, "kernelCpuSeconds": 4.00758, "cpuConsumptionPercent": 151.111, "maxRss": 3692191744, "pageFaults": {"IORequired": 0, "IONotRequired": 1318675}, "fsActivity": {"reads": 608, "writes": 26296}}, "uvthreadResourceUsage": {"userCpuSeconds": 88.3126, "kernelCpuSeconds": 2.71682, "cpuConsumptionPercent": 100.032, "fsActivity": {"reads": 0, "writes": 120}}, "libuv": [], "environmentVariables": {"SHELL": "/bin/bash", "npm_config_timing": "", "npm_config_access": "", "npm_config_save_dev": "", "SESSION_MANAGER": "local/xyzmsi:@/tmp/.ICE-unix/1610,unix/xyzmsi:/tmp/.ICE-unix/1610", "npm_package_devDependencies_eslint_plugin_standard": "^4.0.0", "npm_package_devDependencies_thread_loader": "^3.0.1", "npm_package_dependencies__picc_watermark": "file:./local-packages/@picc/watermark", "npm_config_sign_git_tag": "", "npm_package_devDependencies_svg_sprite_loader": "^6.0.11", "npm_config_before": "", "npm_config_userconfig": "/home/<USER>/.npmrc", "npm_config_global": "", "npm_config_unsafe_perm": "true", "npm_config_fetch_retry_mintimeout": "10000", "XDG_CONFIG_DIRS": "/home/<USER>/.config/kdedefaults:/etc/xdg", "npm_config_cache": "/home/<USER>/.npm", "npm_config_loglevel": "notice", "npm_config_init_author_name": "", "npm_config_send_metrics": "", "XDG_SESSION_PATH": "/org/freedesktop/DisplayManager/Session1", "XDG_MENU_PREFIX": "plasma-", "npm_config_optional": "true", "npm_config_user": "1000", "npm_config_git_tag_version": "true", "npm_package_scripts_build_internetprod": "vue-cli-service build --mode internet --no-module", "npm_package_dependencies_js_cookie": "2.2.0", "TERMINAL_EMULATOR": "JetBrains-JediTerm", "npm_package_scripts_svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "npm_config_rebuild_bundle": "true", "npm_config_always_auth": "", "ICEAUTHORITY": "/run/user/1000/iceauth_oBqOGA", "npm_package_devDependencies__vue_cli_service": "^5.0.0-rc.1", "LANGUAGE": "", "npm_package_devDependencies_eslint_plugin_vue": "^8.0.3", "npm_config_cache_max": "Infinity", "NODE": "/home/<USER>/.volta/tools/image/node/12.1.0/bin/node", "npm_config_searchstaleness": "900", "npm_config_ignore_scripts": "", "npm_config_save_optional": "", "npm_package_devDependencies_autoprefixer": "^9.5.1", "npm_package_dependencies_html2canvas": "^1.4.1", "npm_package_devDependencies_babel_core": "7.0.0-bridge.0", "LC_ADDRESS": "zh_CN.UTF-8", "JAVA_HOME": "/home/<USER>/.sdkman/candidates/java/current", "npm_package_devDependencies_eslint_plugin_html": "^6.2.0", "LC_NAME": "zh_CN.UTF-8", "npm_package_engines_npm": ">= 3.0.0", "npm_config_dev": "", "INPUT_METHOD": "fcitx", "npm_config_browser": "", "npm_package_devDependencies_svgo": "1.2.0", "npm_package_browserslist_0": "> 1%", "npm_package_dependencies_web_plugin": "file:./local-packages/web-plugin", "npm_package_browserslist_1": "last 2 versions", "npm_config_maxsockets": "50", "npm_package_devDependencies_svgo_loader": "^2.2.1", "TERM_SESSION_ID": "74d12bf7-266a-48ec-b8df-0f0bf60e78e2", "MEMORY_PRESSURE_WRITE": "c29tZSAyMDAwMDAgMjAwMDAwMAA=", "npm_package_devDependencies__vue_cli_plugin_eslint": "^5.0.0-rc.1", "__INTELLIJ_COMMAND_HISTFILE__": "/home/<USER>/.cache/JetBrains/WebStorm2025.1/terminal/history/pdfc-web-*******-history2", "npm_package_dependencies_path_browserify": "^1.0.1", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"dev\"],\"original\":[\"run\",\"dev\"]}", "SDKMAN_CANDIDATES_DIR": "/home/<USER>/.sdkman/candidates", "npm_config_ham_it_up": "", "XMODIFIERS": "@im=fcitx", "npm_config_bin_links": "true", "DESKTOP_SESSION": "plasma", "LC_MONETARY": "zh_CN.UTF-8", "npm_package_scripts_new": "plop", "npm_package_devDependencies_eslint_plugin_promise": "^5.1.0", "GTK_RC_FILES": "/etc/gtk/gtkrc:/home/<USER>/.gtkrc:/home/<USER>/.config/gtkrc", "npm_config_allow_same_version": "", "npm_config_globalconfig": "/home/<USER>/.volta/tools/image/node/12.1.0/etc/npmrc", "npm_package_volta_node": "12.1.0", "npm_config_cache_min": "10", "EDITOR": "nano", "npm_config_tag_version_prefix": "v", "npm_package_scripts_test_unit": "jest --clearCache && vue-cli-service test:unit", "npm_config_shell": "/bin/bash", "npm_config_preid": "", "npm_config_usage": "", "npm_package_dependencies_axios": "^1.4.0", "XDG_SEAT": "seat0", "npm_package_devDependencies_svg_inline_loader": "^0.8.2", "PWD": "/home/<USER>/tzlib/renbao/pdfc-web-*******", "npm_config_save_prefix": "^", "npm_config_only": "", "XDG_SESSION_DESKTOP": "KDE", "LOGNAME": "xyz", "npm_package_scripts_build_prod": "vue-cli-service build --modern", "XDG_SESSION_TYPE": "wayland", "npm_config_sign_git_commit": "", "npm_package_devDependencies_compression_webpack_plugin": "^4.0.0", "npm_package_readmeFilename": "README.md", "npm_config_commit_hooks": "true", "npm_package_devDependencies__vue_cli_plugin_vuex": "^5.0.0-rc.1", "PNPM_HOME": "/home/<USER>/.local/share/pnpm", "npm_config_init_module": "/home/<USER>/.npm-init.js", "npm_config_otp": "", "npm_config_editor": "nano", "SYSTEMD_EXEC_PID": "3449", "npm_package_devDependencies__babel_core": "^7.16.5", "npm_package_dependencies_vuex": "^3.6.2", "npm_config_tmp": "/tmp", "npm_config_audit_level": "low", "npm_package_devDependencies_terser_webpack_plugin": "^5.0.3", "npm_config_color": "true", "XAUTHORITY": "/run/user/1000/xauth_CzCifj", "npm_package_devDependencies_babel_plugin_transform_remove_console": "^6.9.4", "npm_package_dependencies_core_js": "^3.8.3", "npm_config_package_lock_only": "", "_VOLTA_TOOL_RECURSION": "1", "ENABLE_IDE_INTEGRATION": "true", "npm_package_dependencies_vue_i18n": "7.3.2", "npm_config_save_prod": "", "SDKMAN_OFFLINE_MODE": "", "npm_package_devDependencies_speed_measure_webpack_plugin": "^1.3.3", "MOTD_SHOWN": "pam", "npm_package_devDependencies_happypack": "^5.0.1", "npm_package_devDependencies_babel_polyfill": "^6.26.0", "npm_package_devDependencies_gzip_loader": "0.0.1", "GTK2_RC_FILES": "/etc/gtk-2.0/gtkrc:/home/<USER>/.gtkrc-2.0:/home/<USER>/.config/gtkrc-2.0", "npm_config_also": "", "npm_config_cidr": "", "HOME": "/home/<USER>", "npm_config_node_version": "12.1.0", "npm_package_dependencies_xss": "^1.0.15", "LC_PAPER": "zh_CN.UTF-8", "LANG": "zh_CN.UTF-8", "npm_package_lint_staged_src_______js_vue__0": "eslint --fix", "npm_package_lint_staged_src_______js_vue__1": "git add", "npm_config_init_author_url": "", "XDG_CURRENT_DESKTOP": "KDE", "npm_config_globalignorefile": "/home/<USER>/.volta/tools/image/node/12.1.0/etc/npmignore", "npm_config_init_license": "ISC", "npm_package_dependencies_uuid": "^8.1.0", "npm_package_version": "4.0.2", "npm_config_cache_lock_stale": "60000", "npm_package_devDependencies_chalk": "2.4.2", "npm_package_dependencies_vue_router": "^3.5.3", "MEMORY_PRESSURE_WATCH": "/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/background.slice/plasma-krunner.service/memory.pressure", "npm_package_devDependencies__babel_eslint_parser": "^7.16.5", "WAYLAND_DISPLAY": "wayland-0", "npm_config_versions": "", "npm_package_dependencies_element_ui": "^2.15.6", "npm_config_proxy": "", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_fetch_retries": "2", "npm_config_git": "git", "npm_package_scripts_test_ci": "npm run lint && npm run test:unit", "npm_package_devDependencies_html_webpack_plugin": "^5.5.0", "npm_config_read_only": "", "npm_config_group": "1000", "npm_package_dependencies_crypto_js": "^4.2.0", "XDG_SEAT_PATH": "/org/freedesktop/DisplayManager/Seat0", "npm_config_unicode": "true", "npm_config_sso_type": "o<PERSON>h", "npm_package_devDependencies_mini_css_extract_plugin": "^0.11.3", "npm_config_cache_lock_retries": "10", "INVOCATION_ID": "7938471a1b4049188bac6248dad258de", "npm_package_devDependencies__vue_vue2_jest": "^27.0.0-alpha.3", "npm_config_local_address": "", "npm_package_dependencies_jsrsasign": "^10.5.25", "npm_config_description": "true", "npm_package_devDependencies_vue_highlightjs": "^1.3.3", "npm_package_dependencies_echarts": "^5.3.3", "MANAGERPID": "1396", "npm_package_devDependencies__vue_cli_plugin_babel": "^5.0.0-rc.1", "npm_package_devDependencies_husky": "1.3.1", "VOLTA_HOME": "/home/<USER>/.volta", "INIT_CWD": "/home/<USER>/tzlib/renbao/pdfc-web-*******", "npm_config_dry_run": "", "npm_package_devDependencies_eslint_plugin_import": "^2.25.3", "npm_config_viewer": "man", "npm_config_offline": "", "npm_config_message": "%s", "KDE_SESSION_UID": "1000", "npm_config_production": "", "npm_package_dependencies_img_async_load": "file:./local-packages/img-async-load", "npm_package_scripts_preview": "node build/index.js --preview", "npm_config_prefer_online": "", "npm_config_link": "", "npm_lifecycle_script": "vue-cli-service serve", "npm_package_description": "```shell\r //内网npm仓库配置\r npm config set sass_binary_site=http://***********:10001/sass/node-sass/releases/download\r npm config set registry=http://**********:8082/repository/npm-all/\r npm install\r ```", "npm_config_shrinkwrap": "true", "npm_package_dependencies_qiankun": "^2.6.3", "XKB_DEFAULT_LAYOUT": "us", "npm_config_force": "", "npm_package_path": "dist/car", "CLAUDE_CODE_SSE_PORT": "46341", "XDG_ACTIVATION_TOKEN": "kwin-2", "npm_config_rollback": "true", "XDG_SESSION_CLASS": "user", "npm_config_save_bundle": "", "ANDROID_HOME": "/home/<USER>/Android/Sdk", "npm_config_onload_script": "", "npm_package_devDependencies_plop": "^3.1.1", "npm_package_devDependencies__babel_polyfill": "^7.7.0", "TERM": "xterm-256color", "LC_IDENTIFICATION": "zh_CN.UTF-8", "npm_package_name": "pdfc3-web", "npm_package_dependencies_vee_validate": "^2.2.13", "npm_config_prefix": "/home/<USER>/.volta/tools/image/node/12.1.0", "npm_config_cert": "", "npm_package_scripts_lint_fix": "eslint --fix --ext", "USER": "xyz", "npm_package_devDependencies_webpack_merge": "^5.4.0", "NDK_HOME": "/home/<USER>/Android/Sdk/ndk/28.0.13004108", "npm_config_heading": "npm", "npm_package_devDependencies_eslint_plugin_node": "^11.1.0", "npm_package_devDependencies_connect": "3.6.6", "npm_package_dependencies_whatwg_fetch": "^3.6.2", "QT_WAYLAND_RECONNECT": "1", "KDE_SESSION_VERSION": "6", "PAM_KWALLET5_LOGIN": "/run/user/1000/kwallet5.socket", "npm_package_dependencies__picc_verifition": "file:./local-packages/@picc/verifition", "npm_package_devDependencies__vue_test_utils": "1.0.0-beta.29", "SDKMAN_ENV": "", "npm_config_cache_lock_wait": "10000", "npm_package_dependencies_register_service_worker": "^1.7.2", "npm_config_node_options": "", "npm_config_key": "", "SDKMAN_DIR": "/home/<USER>/.sdkman", "npm_config_json": "", "npm_config_depth": "Infinity", "DISPLAY": ":1", "npm_package_devDependencies_lint_staged": "8.1.5", "npm_lifecycle_event": "dev", "SHLVL": "1", "CHROME_EXECUTABLE": "/usr/bin/google-chrome-stable", "npm_package_devDependencies_eslint": "^7.32.0", "npm_config_home": "https://npmmirror.com", "LC_TELEPHONE": "zh_CN.UTF-8", "npm_config_ca": "", "npm_config_prefer_offline": "", "LC_MEASUREMENT": "zh_CN.UTF-8", "XDG_VTNR": "1", "SDKMAN_CANDIDATES_API": "https://api.sdkman.io/2", "npm_config_if_present": "", "npm_package_scripts_build_stage": "vue-cli-service build --mode staging", "XDG_SESSION_ID": "2", "npm_package_scripts_build_intranetprod": "vue-cli-service build --mode intranet --no-module", "npm_package_devDependencies_sass_loader": "^10.2.0", "npm_config_user_agent": "npm/6.9.0 node/v12.1.0 linux x64", "npm_package_scripts_lint": "eslint --ext .js --ext .jsx --ext .vue src/", "npm_package_dependencies_vue": "^2.6.14", "npm_package_devDependencies_script_ext_html_webpack_plugin": "^2.1.5", "npm_config_save_exact": "", "npm_package_devDependencies_babel_plugin_transform_runtime": "^6.23.0", "npm_execpath": "/home/<USER>/.volta/tools/image/node/12.1.0/lib/node_modules/npm/bin/npm-cli.js", "npm_config_progress": "true", "npm_package_dependencies_nprogress": "^0.2.0", "npm_config_global_style": "", "npm_config_init_author_email": "", "XDG_RUNTIME_DIR": "/run/user/1000", "npm_config_script_shell": "", "npm_config_long": "", "npm_config_save": "true", "npm_config_strict_ssl": "true", "npm_config_auth_type": "legacy", "npm_config_version": "", "npm_config_logs_max": "10", "npm_package_devDependencies_serve_static": "^1.13.2", "DEBUGINFOD_URLS": "https://debuginfod.archlinux.org ", "npm_package_devDependencies_babel_jest": "27.5.1", "npm_package_devDependencies_vue_template_compiler": "^2.6.14", "npm_package_devDependencies_hard_source_webpack_plugin": "^0.13.1", "LC_TIME": "zh_CN.UTF-8", "npm_package_keywords_5": "admin-template", "npm_package_keywords_4": "boilerplate", "npm_package_keywords_6": "management-system", "npm_package_keywords_1": "admin", "npm_package_scripts_dev": "vue-cli-service serve", "npm_config_umask": "0022", "npm_package_keywords_0": "vue", "npm_package_devDependencies_runjs": "^4.3.2", "npm_package_devDependencies__vue_cli_plugin_router": "^5.0.0-rc.1", "npm_package_keywords_3": "element-ui", "npm_package_keywords_2": "dashboard", "npm_config_sso_poll_frequency": "500", "npm_config_searchopts": "", "npm_config_legacy_bundling": "", "npm_package_dependencies_vuedraggable": "^2.24.3", "JOURNAL_STREAM": "9:39715", "npm_package_husky_hooks_pre_commit": "lint-staged", "KDE_FULL_SESSION": "true", "npm_config_searchlimit": "20", "npm_package_dependencies_base64url": "^3.0.1", "npm_config_noproxy": "", "BROWSER": "google-chrome-stable", "PATH": "/home/<USER>/.volta/tools/image/node/12.1.0/lib/node_modules/npm/node_modules/npm-lifecycle/node-gyp-bin:/home/<USER>/tzlib/renbao/pdfc-web-*******/node_modules/.bin:/home/<USER>/.volta/tools/image/node/12.1.0/bin:/home/<USER>/.volta/bin:/home/<USER>/soft/flutter/bin:/home/<USER>/.local/share/pnpm:/home/<USER>/.sdkman/candidates/java/current/bin:/home/<USER>/.local/bin:/home/<USER>/.cargo/bin:/usr/local/sbin:/usr/local/bin:/usr/bin:/usr/bin/site_perl:/usr/bin/vendor_perl:/usr/bin/core_perl:/usr/lib/rustup/bin:/home/<USER>/.local/share/JetBrains/Toolbox/scripts:/home/<USER>/.cache/lm-studio/bin:/home/<USER>/tzlib/renbao/pdfc-web-*******/node_modules/.bin:/home/<USER>/.cache/lm-studio/bin", "npm_config_metrics_registry": "https://registry.npmmirror.com/", "npm_config_node_gyp": "/home/<USER>/.volta/tools/image/node/12.1.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "npm_config_searchexclude": "", "npm_package_devDependencies_vue_quill_editor": "^3.0.6", "npm_package_devDependencies_sass": "^1.51.0", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1000/bus", "SDKMAN_PLATFORM": "linuxx64", "npm_package_gitHead": "a0b9a3793e65837c5eb3a4f6949cb8bab0494248", "npm_config_update_notifier": "true", "KDE_APPLICATIONS_AS_SCOPE": "1", "npm_package_devDependencies__babel_register": "^7.16.5", "npm_package_scripts_build_performance": "vue-cli-service build --mode performance  --report", "MAIL": "/var/spool/mail/xyz", "npm_config_registry": "https://registry.npmmirror.com/", "npm_config_ignore_prepublish": "", "npm_config_audit": "true", "npm_config_tag": "latest", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_cafile": "", "npm_config_fetch_retry_factor": "10", "npm_node_execpath": "/home/<USER>/.volta/tools/image/node/12.1.0/bin/node", "npm_config_engine_strict": "", "npm_config_https_proxy": "", "LC_NUMERIC": "zh_CN.UTF-8", "npm_package_devDependencies_jest": "^27.1.0", "npm_package_devDependencies__vue_cli_plugin_unit_jest": "^5.0.0-rc.1", "npm_config_scope": "", "npm_package_engines_node": ">=8.9", "npm_config_package_lock": "true", "npm_config_parseable": "", "npm_config_init_version": "1.0.0", "_": "/home/<USER>/tzlib/renbao/pdfc-web-*******/node_modules/.bin/vue-cli-service", "VUE_APP_ENV": "development", "VUE_APP_BASE_API": ".", "VUE_APP_USE_TYPE": "MainApp", "NODE_ENV": "development", "BABEL_ENV": "development", "VUE_CLI_TRANSPILE_BABEL_RUNTIME": "true", "WEBPACK_SERVE": "true", "VUE_CLI_ENTRY_FILES": "[\"/home/<USER>/tzlib/renbao/pdfc-web-*******/src/main.js\"]"}, "userLimits": {"core_file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "data_seg_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "max_locked_memory_bytes": {"soft": 8388608, "hard": 8388608}, "max_memory_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "open_files": {"soft": 524288, "hard": 524288}, "stack_size_bytes": {"soft": 8388608, "hard": "unlimited"}, "cpu_time_seconds": {"soft": "unlimited", "hard": "unlimited"}, "max_user_processes": {"soft": 254823, "hard": 254823}, "virtual_memory_kbytes": {"soft": "unlimited", "hard": "unlimited"}}, "sharedObjects": ["linux-vdso.so.1", "/usr/lib/libdl.so.2", "/usr/lib/librt.so.1", "/usr/lib/libstdc++.so.6", "/usr/lib/libm.so.6", "/usr/lib/libgcc_s.so.1", "/usr/lib/libpthread.so.0", "/usr/lib/libc.so.6", "/lib64/ld-linux-x86-64.so.2"]}