#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 检测操作系统
const isWindows = process.platform === 'win32';

// 内网包列表
const INTERNAL_PACKAGES = [
    '@picc/verifition',
    '@picc/watermark',
    'web-plugin',
    'img-async-load'
];

const LOCAL_PACKAGES_DIR = './local-packages';
const REPO_PACKAGES_DIR = './repo-packages'; // git仓库中的包目录

// 跨平台命令
const commands = {
    mkdir: isWindows ? 'mkdir' : 'mkdir -p',
    rmdir: isWindows ? 'rmdir /s /q' : 'rm -rf',
    copy: isWindows ? 'xcopy /e /i /h /y' : 'cp -r'
};

console.log(`🚀 开始设置本地内网包（${isWindows ? 'Windows' : 'Unix'} 系统）...\n`);

// 跨平台函数
function createDir(dirPath) {
    if (isWindows) {
    // Windows: 创建目录，忽略已存在的错误
        try {
            execSync(`if not exist "${dirPath}" ${commands.mkdir} "${dirPath}"`, { stdio: 'pipe' });
        } catch (error) {
            // 忽略目录已存在的错误
        }
    } else {
        execSync(`${commands.mkdir} "${dirPath}"`, { stdio: 'pipe' });
    }
}

function removeDir(dirPath) {
    if (fs.existsSync(dirPath)) {
        try {
            execSync(`${commands.rmdir} "${dirPath}"`, { stdio: 'pipe' });
        } catch (error) {
            console.warn(`警告: 删除目录失败 ${dirPath}:`, error.message);
        }
    }
}

function copyDir(sourcePath, targetPath) {
    if (isWindows) {
        execSync(`${commands.copy} "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
    } else {
        execSync(`${commands.copy} "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
    }
}

try {
    // 0. 检查repo-packages目录是否存在
    if (!fs.existsSync(REPO_PACKAGES_DIR)) {
        console.log('⚠️  repo-packages 目录不存在');
        console.log('📝 请先创建 repo-packages 目录并放置内网包：');
        console.log('mkdir -p repo-packages/@picc/verifition');
        console.log('mkdir -p repo-packages/@picc/watermark');
        console.log('mkdir -p repo-packages/web-plugin');
        console.log('mkdir -p repo-packages/img-async-load');
        console.log('\n然后将真实的包文件复制到对应目录中');
        process.exit(0);
    }

    // 1. 清理并创建本地包目录
    console.log('📁 清理并创建本地包目录...');
    removeDir(LOCAL_PACKAGES_DIR);
    createDir(LOCAL_PACKAGES_DIR);
    console.log('✅ 创建本地包目录:', LOCAL_PACKAGES_DIR);

    // 2. 复制内网包（从git仓库中的预置目录）
    console.log('\n📦 复制内网包...');
    INTERNAL_PACKAGES.forEach(packageName => {
        const sourcePath = path.join(REPO_PACKAGES_DIR, packageName);
        const targetPath = path.join(LOCAL_PACKAGES_DIR, packageName);

        console.log(`处理包: ${packageName}`);

        if (fs.existsSync(sourcePath)) {
            try {
                // 删除目标目录（如果存在）
                removeDir(targetPath);

                // 创建目标目录的父目录
                const targetDir = path.dirname(targetPath);
                createDir(targetDir);

                // 复制包
                copyDir(sourcePath, targetPath);
                console.log(`✅ 复制成功: ${packageName}`);
            } catch (error) {
                console.error(`❌ 复制失败: ${packageName}`, error.message);
            }
        } else {
            console.warn(`⚠️  包不存在: ${packageName} (${sourcePath})`);
        }
    });

    console.log('\n🎉 本地内网包设置完成！');

} catch (error) {
    console.error('❌ 设置失败:', error.message);
    process.exit(1);
}
